/**
 * CC to AP Custom Fields Processor
 *
 * Handles synchronization of custom fields from CliniCore (CC) to AutoPatient (AP) by:
 * 1. Fetching CC patient custom field data using the custom field IDs
 * 2. Filtering out standard contact fields to prevent conflicts
 * 3. Mapping CC custom field values to AP custom field format with proper Unicode handling
 * 4. Creating AP custom fields with appropriate data types if they don't exist
 * 5. Updating the AP contact with the synchronized custom field values
 *
 * Features:
 * - Unicode-aware field name matching for international characters
 * - Data type mapping between CC and AP custom field types
 * - Prevention of standard contact field conversion to custom fields
 * - Proper handling of multiple values in CC custom fields
 */

import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
	GetCCCustomField,
	GetCCPatientCustomField,
	GetCCPatientType,
	PostAPContactType,
} from "@type";
import { apCustomfield, contactReq, patientReq } from "@/apiClient";
import { logApiError } from "@/utils/errorLogger";

/**
 * Standard contact fields that should not be converted to custom fields
 * Based on PostAPContactType interface to prevent conflicts with core contact data
 */
const STANDARD_CONTACT_FIELDS = [
	"email",
	"phone",
	"name",
	"firstName",
	"lastName",
	"timezone",
	"dnd",
	"source",
	"assignedTo",
	"address1",
	"city",
	"state",
	"country",
	"postalCode",
	"tags",
	"dateOfBirth",
	"ssn",
	"gender",
	"customFields",
	// Common variations and translations
	"first name",
	"last name",
	"date of birth",
	"phone number",
	"email address",
	"postal code",
	"zip code",
	"address",
	"vorname",
	"nachname",
	"geburtsdatum",
	"telefon",
	"e-mail",
	"adresse",
	"postleitzahl",
];

/**
 * Mapping configuration for CC custom fields to AP standard contact fields
 * Maps CC custom field names/labels to AP standard field names
 */
const CC_TO_AP_STANDARD_FIELD_MAPPING: Record<string, string> = {
	// Phone field variations
	"phone-mobile": "phone",
	phonemobile: "phone",
	"phone mobile": "phone",
	"telefon mobil": "phone",
	"telefon-mobil": "phone",
	telefon: "phone",
	mobile: "phone",
	handy: "phone",
	mobiltelefon: "phone",
	"cell phone": "phone",
	"cell-phone": "phone",
	cellular: "phone",
	"mobile phone": "phone",
	"mobile number": "phone",
	"mobile-number": "phone",
	cell: "phone",
	cellphone: "phone",
	handynummer: "phone",
	mobilnummer: "phone",

	// Email field variations (for future use)
	"e-mail": "email",
	"email address": "email",
	"e-mail address": "email",
	"e-mail-adresse": "email",
	"email-adresse": "email",
	"electronic mail": "email",
};

/**
 * CC to AP data type mapping
 * Maps CliniCore custom field types to AutoPatient data types
 */
const CC_TO_AP_DATA_TYPE_MAPPING: Record<string, string> = {
	// Text-based fields
	text: "TEXT",
	textarea: "LARGE_TEXT",
	string: "TEXT",

	// Numeric fields
	number: "NUMERICAL",
	integer: "NUMERICAL",
	decimal: "FLOAT",
	float: "FLOAT",
	currency: "MONETORY",
	money: "MONETORY",

	// Contact fields
	phone: "PHONE",
	telephone: "PHONE",

	// Boolean fields
	boolean: "CHECKBOX",
	checkbox: "CHECKBOX",

	// Selection fields
	select: "SINGLE_OPTIONS",
	dropdown: "SINGLE_OPTIONS",
	radio: "SINGLE_OPTIONS",
	multiselect: "MULTIPLE_OPTIONS",

	// Date/Time fields
	date: "DATE",
	datetime: "DATE",
	time: "TIME",

	// File fields
	file: "FILE_UPLOAD",
	upload: "FILE_UPLOAD",
	attachment: "FILE_UPLOAD",

	// Signature
	signature: "SIGNATURE",

	// Default fallback
	default: "TEXT",
};

/**
 * Interface for custom field mapping result
 */
interface CustomFieldMapping {
	/** AP custom field ID */
	id: string;
	/** Field value to set */
	value: string;
}

/**
 * Normalize string for Unicode-aware comparison
 * Handles German Umlaut characters and other special characters
 */
function normalizeFieldName(name: string): string {
	return name
		.toLowerCase()
		.normalize("NFD")
		.replace(/[\u0300-\u036f]/g, "") // Remove diacritics
		.replace(/[^\w\s]/g, "") // Remove special characters except word chars and spaces
		.replace(/\s+/g, " ") // Normalize whitespace
		.trim();
}

/**
 * Check if two field names match using Unicode-aware comparison
 */
function fieldNamesMatch(name1: string, name2: string): boolean {
	const normalized1 = normalizeFieldName(name1);
	const normalized2 = normalizeFieldName(name2);
	return normalized1 === normalized2;
}

/**
 * Generate AP fieldKey from CC field name
 * Normalizes the field name to match AP's auto-generation pattern
 */
function generateApFieldKey(ccFieldName: string): string {
	const normalizedName = ccFieldName
		.toLowerCase()
		.normalize("NFD")
		.replace(/[\u0300-\u036f]/g, "") // Remove diacritics
		.replace(/[^a-z0-9]/g, "") // Remove all non-alphanumeric characters
		.trim();
	return `${normalizedName}`;
}

/**
 * Find existing AP custom field by name or predicted fieldKey
 * Checks both name matching and potential fieldKey conflicts
 */
function findExistingApField(
	apCustomFields: APGetCustomFieldType[],
	ccField: GetCCCustomField,
): APGetCustomFieldType | undefined {
	const expectedFieldKey = generateApFieldKey(ccField.name);

	return apCustomFields.find((apField) => {
		// Check by name (existing logic)
		const nameMatch =
			fieldNamesMatch(apField.name, ccField.name) ||
			fieldNamesMatch(apField.name, ccField.label);

		// Check by predicted fieldKey to prevent conflicts
		// AP auto-generates fieldKeys, so we predict what it would generate
		const predictedFieldKeyMatch = apField.fieldKey === expectedFieldKey;

		return nameMatch || predictedFieldKeyMatch;
	});
}

/**
 * Enhanced field existence check with API query fallback
 * First checks local cache, then queries AP API if needed
 */
async function findExistingApFieldWithApiCheck(
	apCustomFields: APGetCustomFieldType[],
	ccField: GetCCCustomField,
	requestId: string,
): Promise<APGetCustomFieldType | undefined> {
	// First check local cache
	let existingField = findExistingApField(apCustomFields, ccField);

	if (existingField) {
		console.log(
			`[${requestId}] Found existing field in cache: "${existingField.name}" (ID: ${existingField.id})`,
		);
		return existingField;
	}

	// If not found in cache, refresh from API to ensure we have latest data
	console.log(
		`[${requestId}] Field not found in cache, refreshing AP custom fields from API`,
	);

	try {
		const refreshedFields = await apCustomfield.all();
		existingField = findExistingApField(refreshedFields, ccField);

		if (existingField) {
			console.log(
				`[${requestId}] Found existing field after API refresh: "${existingField.name}" (ID: ${existingField.id})`,
			);
			// Update the cache with fresh data
			apCustomFields.length = 0;
			apCustomFields.push(...refreshedFields);
		}

		return existingField;
	} catch (error) {
		console.error(`[${requestId}] Failed to refresh AP custom fields:`, error);
		return undefined;
	}
}

/**
 * Check if a field name represents a standard contact field
 */
function isStandardContactField(fieldName: string): boolean {
	const normalizedFieldName = normalizeFieldName(fieldName);
	return STANDARD_CONTACT_FIELDS.some(
		(standardField) =>
			normalizeFieldName(standardField) === normalizedFieldName,
	);
}

/**
 * Map CC custom field type to AP data type
 */
function mapCcToApDataType(ccFieldType: string): string {
	const normalizedType = ccFieldType.toLowerCase().trim();
	return (
		CC_TO_AP_DATA_TYPE_MAPPING[normalizedType] ||
		CC_TO_AP_DATA_TYPE_MAPPING.default
	);
}

/**
 * Check if a CC custom field should be mapped to an AP standard field
 * Returns the AP standard field name if mapping exists, null otherwise
 */
function getStandardFieldMapping(
	ccFieldName: string,
	ccFieldLabel: string,
): string | null {
	const normalizedName = normalizeFieldName(ccFieldName);
	const normalizedLabel = normalizeFieldName(ccFieldLabel);

	// Check both field name and label against the mapping
	for (const [ccFieldPattern, apStandardField] of Object.entries(
		CC_TO_AP_STANDARD_FIELD_MAPPING,
	)) {
		const normalizedPattern = normalizeFieldName(ccFieldPattern);
		if (
			normalizedPattern === normalizedName ||
			normalizedPattern === normalizedLabel
		) {
			return apStandardField as string;
		}
	}

	return null;
}

/**
 * Extract standard field mappings from CC custom fields
 * Returns a map of AP standard field names to their values
 */
function extractStandardFieldMappings(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Record<string, string> {
	const standardMappings: Record<string, string> = {};

	console.log(
		`[${requestId}] Extracting standard field mappings from ${ccPatientCustomFields.length} CC custom fields`,
	);

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if this CC custom field should map to an AP standard field
		const apStandardField = getStandardFieldMapping(fieldName, fieldLabel);

		if (apStandardField) {
			// Extract field value
			const fieldValue = extractFieldValues(ccCustomField);

			if (fieldValue && fieldValue.trim() !== "") {
				// Handle multiple mappings to the same standard field - prioritize non-empty values
				if (
					!standardMappings[apStandardField] ||
					standardMappings[apStandardField].trim() === ""
				) {
					standardMappings[apStandardField] = fieldValue.trim();
					console.log(
						`[${requestId}] Standard field mapping: "${fieldName}" (${fieldLabel}) -> AP.${apStandardField} = "${fieldValue.substring(
							0,
							50,
						)}${fieldValue.length > 50 ? "..." : ""}"`,
					);
				} else {
					console.log(
						`[${requestId}] Skipping duplicate standard field mapping: "${fieldName}" (${fieldLabel}) -> AP.${apStandardField} (already mapped)`,
					);
				}
			} else {
				console.log(
					`[${requestId}] Skipping standard field mapping for empty value: ${fieldName} (${fieldLabel})`,
				);
			}
		}
	}

	console.log(
		`[${requestId}] Extracted ${
			Object.keys(standardMappings).length
		} standard field mappings`,
	);
	return standardMappings;
}

/**
 * Extract and convert CC allowed values to AP textBoxListOptions format
 * @param ccField - CC custom field with allowedValues
 * @param currentValue - Current field value to include as an option if not in allowedValues
 * @returns Array of textBoxListOptions for AP custom field
 */
function extractTextBoxListOptions(
	ccField: GetCCCustomField,
	currentValue: string,
): string[] {
	const options: string[] = [];

	console.log(
		`[DEBUG] Extracting options for field ${ccField.label} (type: ${ccField.type})`,
	);
	console.log(
		`[DEBUG] CC allowedValues:`,
		JSON.stringify(ccField.allowedValues, null, 2),
	);

	// Extract allowed values from CC field
	if (ccField.allowedValues && ccField.allowedValues.length > 0) {
		for (const allowedValue of ccField.allowedValues) {
			if (allowedValue.value && allowedValue.value.trim() !== "") {
				const trimmedValue = allowedValue.value.trim();
				options.push(trimmedValue);
				console.log(`[DEBUG] Added option: "${trimmedValue}"`);
			}
		}
	}

	// Ensure current value is included as an option if it's not already present
	if (currentValue && currentValue.trim() !== "") {
		const currentValueTrimmed = currentValue.trim();
		const existingOption = options.find(
			(option) => option.toLowerCase() === currentValueTrimmed.toLowerCase(),
		);

		if (!existingOption) {
			options.push(currentValueTrimmed);
			console.log(
				`[DEBUG] Added current value as option: "${currentValueTrimmed}"`,
			);
		}
	}

	// If no options were found, create a default option based on current value
	if (options.length === 0 && currentValue && currentValue.trim() !== "") {
		const trimmedValue = currentValue.trim();
		options.push(trimmedValue);
		console.log(`[DEBUG] Created default option: "${trimmedValue}"`);
	}

	return options;
}

/**
 * Extract and combine multiple values from CC custom field
 * Handles different separation strategies based on field type
 */
function extractFieldValues(ccCustomField: GetCCPatientCustomField): string {
	if (!ccCustomField.values || ccCustomField.values.length === 0) {
		return "";
	}

	const values = ccCustomField.values
		.map((v) => v.value)
		.filter((v): v is string => v != null && v.trim() !== "");

	if (values.length === 0) {
		return "";
	}

	// For single value, return as-is
	if (values.length === 1) {
		return values[0];
	}

	// For multiple values, choose separation strategy based on field type
	const fieldType = ccCustomField.field.type?.toLowerCase() || "";

	if (fieldType.includes("multiselect") || fieldType.includes("checkbox")) {
		// Use comma separation for multi-select fields
		return values.join(", ");
	} else if (fieldType.includes("textarea") || fieldType.includes("text")) {
		// Use newline separation for text areas
		return values.join("\n");
	} else {
		// Default to comma separation
		return values.join(", ");
	}
}

/**
 * Synchronize custom fields from CC patient to AP contact
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param localPatientId - Local database patient ID for logging context
 * @param ccPatientData - CC patient data containing custom field IDs
 * @param apContactId - AP contact ID to update with custom fields
 * @returns Promise<void> - Completes sync or throws error
 */
export async function syncCcToApCustomFields(
	requestId: string,
	localPatientId: string,
	ccPatientData: GetCCPatientType,
	apContactId: string,
): Promise<void> {
	console.log(
		`[${requestId}] Starting custom field sync for CC Patient ${ccPatientData.id} -> AP Contact ${apContactId} (Local Patient ID: ${localPatientId})`,
	);

	// Step 1: Check if patient has custom fields
	if (!ccPatientData.customFields || ccPatientData.customFields.length === 0) {
		console.log(
			`[${requestId}] No custom fields found for CC patient ${ccPatientData.id}`,
		);
		return;
	}

	try {
		// Step 2: Fetch CC patient custom field data
		console.log(
			`[${requestId}] Fetching ${ccPatientData.customFields.length} custom fields from CC`,
		);
		const ccPatientCustomFields = await patientReq.customFields(
			ccPatientData.customFields,
		);

		if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
			console.log(`[${requestId}] No custom field data returned from CC`);
			return;
		}

		// Step 3: Extract and apply standard field mappings
		const standardFieldMappings = extractStandardFieldMappings(
			ccPatientCustomFields,
			requestId,
		);

		// Build update payload for standard fields
		const standardFieldUpdate: Partial<PostAPContactType> = {};

		// Apply standard field mappings to AP contact if any exist
		if (Object.keys(standardFieldMappings).length > 0) {
			console.log(
				`[${requestId}] Applying ${
					Object.keys(standardFieldMappings).length
				} standard field mappings to AP contact`,
			);

			for (const [fieldName, value] of Object.entries(standardFieldMappings)) {
				if (fieldName === "phone") {
					standardFieldUpdate.phone = value;
				} else if (fieldName === "email") {
					standardFieldUpdate.email = value;
				}
				// Add more standard field mappings as needed
			}
		}

		// Step 4: Filter out excluded fields and extract valid custom field mappings
		const validCustomFields = await filterAndMapCustomFields(
			ccPatientCustomFields,
			requestId,
		);

		if (validCustomFields.length === 0) {
			console.log(
				`[${requestId}] No valid custom fields to sync after filtering`,
			);
			return;
		}

		// Step 4: Get all AP custom fields for mapping
		console.log(`[${requestId}] Fetching AP custom fields for mapping`);
		const apCustomFields = await apCustomfield.all();

		// Step 5: Map CC fields to AP format and create missing fields
		const apCustomFieldMappings = await mapToApCustomFields(
			validCustomFields,
			apCustomFields,
			requestId,
		);

		if (apCustomFieldMappings.length === 0) {
			console.log(`[${requestId}] No custom field mappings created`);
			return;
		}

		// Step 6: Update AP contact with custom fields
		console.log(
			`[${requestId}] Updating AP contact with ${apCustomFieldMappings.length} custom fields`,
		);
		let updatePayload: Partial<PostAPContactType> = {
			customFields: apCustomFieldMappings,
		};
		if (
			Object.keys(standardFieldMappings).length > 0 &&
			Object.keys(standardFieldUpdate).length > 0
		) {
			updatePayload = {
				...updatePayload,
				...standardFieldUpdate,
			};
		}
		await contactReq.update(apContactId, updatePayload);

		console.log(`[${requestId}] Custom field sync completed successfully`);
	} catch (error) {
		console.error(`[${requestId}] Custom field sync failed:`, error);

		// Log the error but don't throw to avoid blocking main patient processing
		await logApiError(
			error as Error,
			requestId,
			"custom_field_sync",
			"cc_to_ap_sync",
			{
				ccPatientId: ccPatientData.id,
				apContactId,
				customFieldCount: ccPatientData.customFields?.length || 0,
			},
		);

		// Re-throw to let caller decide how to handle
		throw error;
	}
}

/**
 * Filter CC custom fields and extract valid field mappings
 * Excludes standard contact fields and empty values
 *
 * @param ccPatientCustomFields - CC patient custom field data
 * @param requestId - Request ID for logging
 * @returns Promise<Array> - Valid custom field data
 */
async function filterAndMapCustomFields(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Promise<Array<{ field: GetCCCustomField; value: string }>> {
	const validFields: Array<{ field: GetCCCustomField; value: string }> = [];

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if field is a standard contact field that should not be converted to custom field
		if (
			isStandardContactField(fieldName) ||
			isStandardContactField(fieldLabel)
		) {
			console.log(
				`[${requestId}] Excluding standard contact field: ${fieldName} (${fieldLabel})`,
			);
			continue;
		}

		// Check if field should be mapped to an AP standard field instead of custom field
		const standardFieldMapping = getStandardFieldMapping(fieldName, fieldLabel);
		if (standardFieldMapping) {
			console.log(
				`[${requestId}] Excluding field mapped to standard field: ${fieldName} (${fieldLabel}) -> AP.${standardFieldMapping}`,
			);
			continue;
		}

		// Extract field value using improved multiple values handling
		const fieldValue = extractFieldValues(ccCustomField);

		if (!fieldValue || fieldValue.trim() === "") {
			console.log(
				`[${requestId}] Skipping field with empty value: ${fieldName}`,
			);
			continue;
		}

		validFields.push({
			field: ccCustomField.field,
			value: fieldValue,
		});

		console.log(
			`[${requestId}] Valid field: ${fieldName} (${fieldLabel}) = ${fieldValue.substring(
				0,
				100,
			)}${fieldValue.length > 100 ? "..." : ""}`,
		);
	}

	return validFields;
}

/**
 * Map CC custom fields to AP custom field format
 * Creates missing AP custom fields with proper data types as needed
 *
 * @param validCustomFields - Filtered CC custom field data
 * @param apCustomFields - Existing AP custom fields
 * @param requestId - Request ID for logging
 * @returns Promise<CustomFieldMapping[]> - AP custom field mappings
 */
async function mapToApCustomFields(
	validCustomFields: Array<{ field: GetCCCustomField; value: string }>,
	apCustomFields: APGetCustomFieldType[],
	requestId: string,
): Promise<CustomFieldMapping[]> {
	const mappings: CustomFieldMapping[] = [];
	let createdCount = 0;
	let existingCount = 0;

	console.log(
		`[${requestId}] Starting custom field mapping for ${validCustomFields.length} CC fields against ${apCustomFields.length} existing AP fields`,
	);

	for (const { field, value } of validCustomFields) {
		try {
			// Try to find existing AP custom field using enhanced matching with API fallback
			let apCustomField = await findExistingApFieldWithApiCheck(
				apCustomFields,
				field,
				requestId,
			);

			// Create AP custom field if it doesn't exist
			if (!apCustomField) {
				const mappedDataType = mapCcToApDataType(field.type);
				const fieldKey = generateApFieldKey(field.name);

				console.log(
					`[${requestId}] Creating new AP custom field: "${field.label}" with fieldKey: "${fieldKey}" (type: ${field.type} -> ${mappedDataType})`,
				);

				const createData: APPostCustomfieldType = {
					name: field.label, // Use CC label as AP name (user-friendly)
					dataType: mappedDataType,
					model: "contact", // Set model to contact as required
					fieldKey: fieldKey, // Use CC name for fieldKey (normalized)
				};

				// Add conditional properties based on data type
				if (
					mappedDataType === "SINGLE_OPTIONS" ||
					mappedDataType === "MULTIPLE_OPTIONS"
				) {
					// Extract allowed values from CC field and convert to AP format
					const textBoxListOptions = extractTextBoxListOptions(field, value);

					if (textBoxListOptions.length > 0) {
						// Based on successful test: API expects "options" as simple string array
						(createData as any).options = textBoxListOptions;

						console.log(
							`[${requestId}] Adding ${textBoxListOptions.length} options to ${
								field.label
							}: ${textBoxListOptions.join(", ")}`,
						);
						console.log(
							`[${requestId}] Using simple string array for options (confirmed working structure)`,
						);
					} else {
						// If no options can be created, fall back to TEXT type to avoid API error
						console.log(
							`[${requestId}] No options available for ${field.label}, falling back to TEXT type`,
						);
						createData.dataType = "TEXT";
					}
				}

				try {
					apCustomField = await apCustomfield.create(createData);

					// Add to our local cache to avoid duplicate creation
					apCustomFields.push(apCustomField);

					console.log(
						`[${requestId}] Successfully created AP custom field: "${field.label}" with ID: ${apCustomField.id}`,
					);
					createdCount++;
				} catch (createError: unknown) {
					const errorMessage =
						createError instanceof Error
							? createError.message
							: String(createError);

					// Handle duplicate fieldKey error by trying to find the existing field
					if (errorMessage.includes("already exists")) {
						console.log(
							`[${requestId}] Field with fieldKey "${fieldKey}" already exists, attempting to find existing field`,
						);

						// Use enhanced field finding with fresh API data
						apCustomField = await findExistingApFieldWithApiCheck(
							apCustomFields,
							field,
							requestId,
						);

						if (apCustomField) {
							console.log(
								`[${requestId}] Found existing AP custom field after duplicate error: "${apCustomField.name}" with ID: ${apCustomField.id}`,
							);
							existingCount++;
						} else {
							console.error(
								`[${requestId}] Could not find existing field after duplicate error for: "${field.name}" with fieldKey: "${fieldKey}"`,
							);
							throw createError;
						}
					} else {
						throw createError;
					}
				}
			} else {
				console.log(
					`[${requestId}] Using existing AP custom field: "${apCustomField.name}" with ID: ${apCustomField.id}`,
				);
				existingCount++;
			}

			mappings.push({
				id: apCustomField.id,
				value: value,
			});

			console.log(
				`[${requestId}] Mapped: "${field.label}" -> AP Field ID ${
					apCustomField.id
				} (${value.substring(0, 50)}${value.length > 50 ? "..." : ""})`,
			);
		} catch (error) {
			console.error(
				`[${requestId}] Failed to map field "${field.name}":`,
				error,
			);
			// Continue with other fields rather than failing completely
		}
	}

	console.log(
		`[${requestId}] Custom field mapping completed: ${mappings.length} total mappings (${createdCount} created, ${existingCount} existing)`,
	);

	return mappings;
}
